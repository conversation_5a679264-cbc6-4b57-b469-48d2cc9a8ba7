# -*- coding: utf-8 -*-
import logging
import requests
import re
import json
from odoo import models, fields, api, _
from odoo.exceptions import UserError
from .pydantic_models import BlogPostContent
from ..services.odoo_synchronization_service import OdooSynchronizationService
import base64
import re
import tempfile


_logger = logging.getLogger(__name__)

try:
    from pytubefix import YouTube
    from pptx import Presentation
    from pptx.util import Pt
    from bs4 import BeautifulSoup
    from pydantic import ValidationError
except ImportError:
    _logger.warning("One or more required libraries (pytubefix, pydantic) are not found. Please install them.")
    YouTube = None
    ValidationError = None

class BlogGenerationRequest(models.Model):
    _name = 'blog.generation.request'
    _description = 'Blog Generation Request'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    name = fields.Char(string='Title', required=True)
    blog_id = fields.Many2one('blog.blog', string='Blog', ondelete='cascade')
    website_id = fields.Many2one('website', string='Website', ondelete='cascade')
    source_type = fields.Selection([
        ('youtube', 'YouTube URL'),
        ('github', 'GitHub Repository'),
        ('topic', 'Topic Description')
    ], string='Source Type', required=True, default='topic')

    youtube_url = fields.Char(string='YouTube URL')
    github_repo_path = fields.Char(string='GitHub Repository Path')
    topic_description = fields.Text(string='Topic Description')
    additional_instructions = fields.Text(string='Additional Instructions', help="Provide specific instructions for the AI, e.g., 'Summarize this into 5 key points' or 'Write in a casual, friendly tone'.")
    image = fields.Image(string="Featured Image", help="Upload an image to be included in the blog post.")
    image_placement = fields.Selection([
        ('top', 'Top of Post'),
        ('bottom', 'Bottom of Post'),
        ('no_image', 'Do Not Include Image')
    ], string="Image Placement", default='top', help="Where to place the featured image in the blog post.")
 
    provider_type = fields.Selection(
    related='model_id.provider_type',
    )

    model_id = fields.Many2one(
    'llm.model',
    string='Model',
    required=True,
    domain="[('id', 'in', available_model_ids)]",
    )

    available_model_ids = fields.Many2many(
        'llm.model',
        compute='_compute_available_models',
        string='Available Models',
        store=False,
    )

    # ppt genration Field 
    ppt_attachment_id = fields.Many2one('ir.attachment', string="Generated PPT")


    state = fields.Selection([
        ('draft', 'Draft'),
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed')
    ], string='Status', default='draft', readonly=True, copy=False, tracking=True)

    blog_post_id = fields.Many2one('blog.post', string='Generated Blog Post', readonly=True)
    error_message = fields.Text(string='Error Message', readonly=True)
    use_queue_job = fields.Boolean(string="Use Queue Job", help="If checked, the generation will be processed in the background.")
    odoo_connection_id = fields.Many2one('odoo.connection', string='Remote Odoo Connection', help="Select a remote Odoo instance to sync the blog post to.")

    # Social Media Content
    linkedin_post = fields.Text(string='LinkedIn Post', readonly=True, copy=False)
    facebook_post = fields.Text(string='Facebook Post', readonly=True, copy=False)
    twitter_post = fields.Text(string='Twitter/X Post', readonly=True, copy=False)

    def action_generate_blog_post(self):
        self.ensure_one()
        if not self.blog_id:
            raise UserError(_("Please select a Blog before generating the post."))
        self.write({'state': 'pending'})
        if self.use_queue_job:
            self.with_delay()._generate_blog_post()
        else:
            self._generate_blog_post()

    def action_generate_ppt(self):
        self.ensure_one()

        if not self.blog_post_id:
            raise UserError(_("Blog post must be generated first."))

        attachment = self.generate_ppt_from_blog(self.blog_post_id)
        self.ppt_attachment_id = attachment.id

        return {
            'type': 'ir.actions.act_url',
            'url': f"/web/content/{attachment.id}?download=true",
            'target': 'new'
        }

    @api.depends('model_id')
    def _compute_available_models(self):
        Provider = self.env['llm.provider']
        Model = self.env['llm.model']

        # Get active providers with API keys
        active_providers = Provider.search([
            ('api_key', '!=', False),
            ('active', '=', True),
            ('state', '=', 'processing'), 
        ])

        configured_types = list(set(active_providers.mapped('provider_type')))
        allowed_models = Model.search([
            ('provider_type', 'in', configured_types),
            ('active', '=', True),
        ])
        for rec in self:
            rec.available_model_ids = allowed_models

    def _get_youtube_transcript(self):
        self.ensure_one()

        if not self.youtube_url:
            raise UserError(_("YouTube URL is not set."))
        if not YouTube:
            raise UserError(_("pytubefix is not installed."))
        try:
            yt = YouTube(self.youtube_url)
            _logger.info("Video title: %s", yt.title)

            if not yt.captions:
                _logger.warning("No captions found in the video.")
                return _("No captions found for this video.")

            _logger.info("Available captions: %s", yt.captions)

            # Try to get English or Auto-generated English
            caption = yt.captions.get('en') or yt.captions.get('a.en')
            if not caption:
                caption = list(yt.captions.values())[0]
                _logger.warning("Defaulting to first available caption: %s", caption.code)

            transcript = caption.generate_srt_captions()
            return transcript or _("Transcript is empty.")

        except Exception as e:
            _logger.error("Failed to fetch YouTube transcript: %s", str(e))
            raise UserError(_("Could not fetch transcript. Error: %s") % str(e))

    def _get_github_content(self):
        self.ensure_one()
        if not self.github_repo_path:
            raise UserError(_("GitHub path is not set."))
        # Assuming a raw content URL like https://raw.githubusercontent.com/user/repo/branch/file.py
        try:
            response = requests.get(self.github_repo_path, timeout=10)
            if response.status_code == 404:
                raise UserError(_("Could not access the GitHub file. This may be because it is in a private repository, which is not supported. Please use a URL from a public repository."))
            response.raise_for_status()
            return response.text
        except requests.exceptions.RequestException as e:
            _logger.error("Failed to fetch GitHub content: %s", str(e))
            raise UserError(_("Could not fetch content from GitHub. Error: %s") % str(e))

    def copy(self, default=None):
        if default is None:
            default = {}
        if not default.get('name'):
            default['name'] = _("%s (Copy)") % self.name
        default['state'] = 'draft'
        default['blog_post_id'] = False
        default['error_message'] = False
        default['linkedin_post'] = False
        default['facebook_post'] = False
        default['twitter_post'] = False
        return super(BlogGenerationRequest, self).copy(default)

    def _prepare_prompt(self):
        self.ensure_one()
        source_content = ""
        prompt_instruction = ""

        if self.source_type == 'youtube':
            source_content = self._get_youtube_transcript()
            prompt_instruction = "Based on the following YouTube transcript, write a comprehensive and engaging blog post."
        elif self.source_type == 'github':
            source_content = self._get_github_content()
            prompt_instruction = "From the GitHub content provided below, create a technical blog post. Explain the code's purpose, how it works, and provide usage examples. Assume the audience is other developers."
        elif self.source_type == 'topic':
            source_content = self.topic_description
            prompt_instruction = "Write a detailed blog post on the following topic."

        if not source_content:
            return ""

        # Enhanced HTML formatting instruction for Odoo Website
        html_instruction = (
            "Generate the blog post content as a JSON object that strictly follows this Pydantic model:\n"
            f"```json\n{BlogPostContent.model_json_schema()}\n```\n"
            "Ensure the 'html_content' is well-structured for an Odoo website blog, using appropriate tags for headings, paragraphs, lists, and code blocks.\n"
            "IMPORTANT: The 'html_content' field must ONLY contain the HTML body of the blog post. Do NOT include any other JSON fields, keys, or values inside the 'html_content' string."
        )
        html_and_ppt_instruction = (
            "Generate AI content in JSON format following this Pydantic model:\n\n"
            "```json\n"
            "{\n"
            "  \"title\": \"<Blog Post Title>\",\n"
            "  \"subtitle\": \"<Blog Subtitle>\",\n"
            "  \"html_content\": \"<Well-structured blog post HTML>\",\n"
            "  \"ppt_content\": {\n"
            "    \"slides\": [\n"
            "      {\n"
            "        \"title\": \"<Slide Title>\",\n"
            "        \"bullet_points\": [\"<Point 1>\", \"<Point 2>\", ...]\n"
            "      },\n"
            "      ...\n"
            "    ]\n"
            "  },\n"
            "}\n"
            "```\n\n"
            "Ensure:\n"
            "- The `html_content` field contains clean, well-formatted HTML for blogs.\n"
            "- The `ppt_content` provides a clear slide deck structure with `Intro` and ending with a `Thank You` slide.\n"
            "- Use bullet points per slide with logical, digestible breakdowns.\n"
            "- Use engaging titles and technical explanations if applicable.\n"
        )


        full_prompt = f"{prompt_instruction}\n\n{html_instruction}\n\n{html_and_ppt_instruction}\n\nHere is the content to work with:\n\n---\n{source_content}\n---"

        if self.additional_instructions:
            full_prompt += f"\n\nPlease also follow these specific instructions:\n{self.additional_instructions}"
            
        return full_prompt

    def _extract_json_from_llm_output(self, content):
        """
        Extracts a JSON string from the raw LLM response.
        - Handles markdown code fences (e.g., ```json ... ```).
        - Finds the JSON object within the text.
        """
        # The LLM might wrap the JSON in ```json ... ``` or start with 'json'.
        match = re.search(r'```json\s*(.*?)\s*```', content, re.DOTALL | re.IGNORECASE)
        if match:
            content = match.group(1)
        elif content.strip().lower().startswith('json'):
            content = content.strip()[4:].strip()

        # The core JSON object starts with { and ends with }.
        # We look for the first '{' and the last '}' to get the full object.
        start = content.find('{')
        end = content.rfind('}')
        if start != -1 and end != -1 and end > start:
            return content[start:end+1].strip()

        return content.strip()  # Fallback

    def _generate_blog_post(self):
        self.ensure_one()
        try:
            provider = self.env['llm.provider'].search([
                ('provider_type', '=', self.model_id.provider_type),
            ], limit=1)

            if not provider:
                raise UserError(_("No LLM provider is configured for the selected model."))

            llm_provider = provider

            prompt = self._prepare_prompt()
            if not prompt:
                raise UserError(_("Could not generate a prompt from the source."))

            # Generate Blog Content
            raw_response = llm_provider.generate(prompt=prompt)

            # Clean and parse the JSON response
            try:
                clean_json_str = self._extract_json_from_llm_output(raw_response)
                # Use strict=False to allow control characters like newlines inside the JSON strings,
                # as LLMs often generate multi-line strings for HTML content.
                response_data = json.loads(clean_json_str, strict=False)
                parsed_content = BlogPostContent(**response_data)

                post_title = parsed_content.title or self.name
                html_content = parsed_content.html_content

                # Defensively clean the HTML content, as the LLM sometimes includes trailing JSON structure.
                garbage_match = re.search(r'",\s*"(subtitle|tags|image_caption|seo)":', html_content)
                if garbage_match:
                    html_content = html_content[:garbage_match.start()].strip()
                
                image_caption = parsed_content.image_caption
                subtitle = parsed_content.subtitle
                tags = parsed_content.tags
                meta_title = parsed_content.seo.meta_title
                meta_description = parsed_content.seo.meta_description
                meta_keywords = ", ".join(parsed_content.seo.meta_keywords)

                # Extract social media content
                linkedin_post = parsed_content.social_media.linkedin_post
                facebook_post = parsed_content.social_media.facebook_post
                twitter_post = parsed_content.social_media.twitter_post

            except (json.JSONDecodeError, ValidationError, AttributeError) as e:
                _logger.error("Failed to parse LLM response: %s. Raw response: %s", e, raw_response)
                raise UserError(_(
                    "Failed to parse the response from the AI. The response was not valid JSON. "
                    "Please try again or check the logs for the raw response.\n\n"
                    "Error: %s\n\nRaw Response:\n%s"
                ) % (e, raw_response))

            # Create the blog post first
            blog_post_vals = {
                'name': post_title,
                'subtitle': subtitle,
                'content': '<p>Generating content...</p>', # Placeholder content
                'blog_id': self.blog_id.id,
                'website_id': self.website_id.id,
                'website_meta_title': meta_title,
                'website_meta_description': meta_description,
                'website_meta_keywords': meta_keywords,
                'is_published': False,
            }
            blog_post = self.env['blog.post'].create(blog_post_vals)

            # Handle tags
            if tags:
                tag_ids = []
                for tag_name in tags:
                    tag = self.env['blog.tag'].search([('name', '=ilike', tag_name)], limit=1)
                    if not tag:
                        tag = self.env['blog.tag'].create({'name': tag_name})
                    tag_ids.append(tag.id)
                if tag_ids:
                    blog_post.write({'tag_ids': [(6, 0, tag_ids)]})

            # Handle image attachment and injection
            if self.image and self.image_placement != 'no_image':
                attachment = self.env['ir.attachment'].create({
                    'name': f"blog_post_image_{blog_post.id}",
                    'res_model': 'blog.post',
                    'res_id': blog_post.id,
                    'datas': self.image,
                    'public': True,
                })
                image_url = f"/web/image/{attachment.id}"
                
                figure_html = f"""
                <figure class="mt-2 mb-4">
                    <img src="{image_url}" class="img-fluid w-100"/>
                    <figcaption class="figure-caption text-muted">{image_caption}</figcaption>
                </figure>
                """
                if self.image_placement == 'top':
                    html_content = figure_html + html_content
                else: # bottom
                    html_content += figure_html

            # Update the blog post with the final content
            blog_post.write({'content': html_content})
            ppt_data = parsed_content.ppt_content
            ppt_attachment = self.generate_ppt_from_blog(blog_post)
        
            self.write({
                'state': 'completed',
                'blog_post_id': blog_post.id,
                'ppt_attachment_id': ppt_attachment.id,
                'error_message': False,
                'linkedin_post': linkedin_post,
                'facebook_post': facebook_post,
                'twitter_post': twitter_post,
            })

            if self.odoo_connection_id:
                self._sync_to_remote(blog_post)

        except Exception as e:
            _logger.error("Failed to generate blog post: %s", str(e))
            self.write({
                'state': 'failed',
                'error_message': str(e),
            })

    def generate_ppt_from_blog(self, blog_post):
        self.ensure_one()

        # Extract content from blog post HTML
        html_content = blog_post.content or ""
        title = blog_post.name or "Blog Presentation"
        subtitle = blog_post.subtitle or ""
        source_type_label = dict(self._fields['source_type'].selection).get(self.source_type, "Unknown Source")

        # Use BeautifulSoup to strip HTML and get plain text
        soup = BeautifulSoup(html_content, "html.parser")
        paragraphs = soup.find_all(['p', 'li', 'h2', 'h3'])

        # Create a presentation
        prs = Presentation()
        bullet_layout = prs.slide_layouts[1]
        title_slide_layout = prs.slide_layouts[0]

        # Slide 1: Title Slide
        slide = prs.slides.add_slide(title_slide_layout)
        slide.shapes.title.text = title
        slide.placeholders[1].text = f"{subtitle}\n\nSource: {source_type_label}"

        # Slide 2+: Content Slides
        current_slide = prs.slides.add_slide(bullet_layout)
        content_shape = current_slide.shapes.placeholders[1]
        tf = content_shape.text_frame
        tf.clear()

        char_limit = 600  # for breaking content into slides
        buffer = ""

        for tag in paragraphs:
            text = tag.get_text(strip=True)
            if not text:
                continue
            if len(buffer + text) > char_limit:
                p = tf.add_paragraph()
                p.text = buffer.strip()
                p.font.size = Pt(16)
                buffer = text
                current_slide = prs.slides.add_slide(bullet_layout)
                tf = current_slide.shapes.placeholders[1].text_frame
                tf.clear()
            else:
                buffer += f"\n- {text}"

        if buffer:
            p = tf.add_paragraph()
            p.text = buffer.strip()
            p.font.size = Pt(16)

        # Save to temp file
        with tempfile.NamedTemporaryFile(delete=False, suffix=".pptx") as tmp_file:
            prs.save(tmp_file.name)
            tmp_file.seek(0)
            ppt_data = tmp_file.read()

        # Create attachment
        attachment = self.env['ir.attachment'].create({
            'name': f"{title}.pptx",
            'res_model': self._name,
            'res_id': self.id,
            'type': 'binary',
            'datas': base64.b64encode(ppt_data),
            'mimetype': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        })

        self.ppt_attachment_id = attachment.id
        return attachment
    
    def action_reset_to_draft(self):
        self.ensure_one()
        self.write({
            'state': 'draft',
            'error_message': False,
            'linkedin_post': False,
            'facebook_post': False,
            'twitter_post': False,
        })

    def action_sync_to_remote(self):
        for request in self:
            if request.state == 'completed' and request.blog_post_id and request.odoo_connection_id:
                request._sync_to_remote(request.blog_post_id)
            else:
                raise UserError(_("The blog post must be in the 'Completed' state and have a remote connection selected to be synced."))

    def _sync_to_remote(self, blog_post):
        """
        Synchronizes the blog post to the remote Odoo instance.
        """
        self.ensure_one()
        sync_service = OdooSynchronizationService(self.odoo_connection_id)
        blog_post_data = {
            'name': blog_post.name,
            'subtitle': str(blog_post.subtitle or ''),
            'content': str(blog_post.content or ''),
            'blog_id': blog_post.blog_id.name,
            'website_id': blog_post.website_id.name,
            'website_meta_title': blog_post.website_meta_title,
            'website_meta_description': blog_post.website_meta_description,
            'website_meta_keywords': blog_post.website_meta_keywords,
            'is_published': False,
            'tag_ids': [tag.name for tag in blog_post.tag_ids],
        }
        remote_id = sync_service.create_blog_post(blog_post_data)
        if remote_id:
            self.message_post(body=f"Successfully synced blog post to remote instance. Remote ID: {remote_id}")
        else:
            self.message_post(body="Failed to sync blog post to remote instance.")